<div class="container mt-4" [ngClass]="{'fade-in': !isLoading}">
  <!-- Loading Spinner -->
  <app-loading-spinner
    *ngIf="isLoading"
    [overlay]="true"
    [sidebarAware]="true"
    [showText]="true"
    text="Üye filtreleri yükleniyor">
  </app-loading-spinner>

  <!-- Main Content -->
  <div *ngIf="!isLoading" class="main-content">
    <div class="row">
    <!-- Filtreler ve İstatistikler -->
    <div class="col-md-3">
      <div class="modern-card filter-card slide-in-left">
        <div class="modern-card-header">
          <h5 class="mb-0">
            <i class="fas fa-filter me-2"></i>
            Filtreler
          </h5>
        </div>
        
        <div class="modern-card-body">
          <!-- Cinsiyet Filtreleri -->
          <div class="filter-section">
            <h6 class="filter-title">
              <i class="fas fa-venus-mars me-2"></i>
              Cinsiyet Filtreleri
            </h6>
            
            <div class="modern-radio-group">
              <div class="modern-radio">
                <input
                  type="radio"
                  id="gender-all"
                  class="modern-radio-input"
                  name="genderFilter"
                  [(ngModel)]="genderFilter"
                  [value]="''"
                  [disabled]="genderFiltersDisabled"
                  (change)="onGenderFilterChange()"
                />
                <label class="modern-radio-label" for="gender-all" [class.disabled]="genderFiltersDisabled">
                  <span class="radio-icon"></span>
                  Tümü
                </label>
              </div>

              <div class="modern-radio">
                <input
                  type="radio"
                  id="gender-male"
                  class="modern-radio-input"
                  name="genderFilter"
                  [(ngModel)]="genderFilter"
                  value="1"
                  [disabled]="genderFiltersDisabled"
                  (change)="onGenderFilterChange()"
                />
                <label class="modern-radio-label" for="gender-male" [class.disabled]="genderFiltersDisabled">
                  <span class="radio-icon"></span>
                  <i class="fas fa-male text-primary me-1"></i>
                  Erkek
                  <span class="modern-badge modern-badge-primary ms-2">{{ genderCounts.male }}</span>
                </label>
              </div>

              <div class="modern-radio">
                <input
                  type="radio"
                  id="gender-female"
                  class="modern-radio-input"
                  name="genderFilter"
                  [(ngModel)]="genderFilter"
                  value="2"
                  [disabled]="genderFiltersDisabled"
                  (change)="onGenderFilterChange()"
                />
                <label class="modern-radio-label" for="gender-female" [class.disabled]="genderFiltersDisabled">
                  <span class="radio-icon"></span>
                  <i class="fas fa-female text-danger me-1"></i>
                  Kadın
                  <span class="modern-badge modern-badge-danger ms-2">{{ genderCounts.female }}</span>
                </label>
              </div>
            </div>
          </div>
          
          <!-- Branş Filtreleri -->
          <div class="filter-section">
            <h6 class="filter-title">
              <i class="fas fa-dumbbell me-2"></i>
              Branş Filtreleri
            </h6>

            <div class="modern-radio-group">
              <div class="modern-radio">
                <input
                  type="radio"
                  id="branch-all"
                  class="modern-radio-input"
                  name="branchFilter"
                  [(ngModel)]="branchFilter"
                  [value]="''"
                  [disabled]="branchFiltersDisabled"
                  (change)="onBranchFilterChange()"
                />
                <label class="modern-radio-label" for="branch-all" [class.disabled]="branchFiltersDisabled">
                  <span class="radio-icon"></span>
                  Tümü
                </label>
              </div>

              <ng-container *ngFor="let type of membershipTypes; let i = index">
                <div class="branch-container" *ngIf="branchCounts[type.branch] > 0">
                  <!-- Branş Radio Button -->
                  <div class="modern-radio branch-radio">
                    <input
                      type="radio"
                      [id]="'branch-' + i"
                      class="modern-radio-input"
                      name="branchFilter"
                      [(ngModel)]="branchFilter"
                      [value]="type.branch"
                      [disabled]="branchFiltersDisabled"
                      (change)="onBranchFilterChange(); onBranchToggle(type.branch)"
                    />
                    <label class="modern-radio-label branch-label" [for]="'branch-' + i"
                           [class.disabled]="branchFiltersDisabled"
                           (click)="onBranchLabelClick(type.branch, $event)">
                      <span class="radio-icon"></span>
                      {{ type.branch }}
                      <span class="modern-badge modern-badge-info ms-2">{{ branchCounts[type.branch] }}</span>
                      <i class="fas fa-chevron-down expand-icon"
                         [class.expanded]="expandedBranches.has(type.branch)"></i>
                    </label>
                  </div>

                  <!-- Paket Filtreleri (Accordion) -->
                  <div class="package-filters"
                       [class.expanded]="expandedBranches.has(type.branch)"
                       *ngIf="expandedBranches.has(type.branch)">
                    <div class="package-list">
                      <div class="package-item"
                           *ngFor="let package of getPackagesForBranch(type.branch)">
                        <div class="modern-checkbox">
                          <input
                            type="checkbox"
                            [id]="'package-' + package.membershipTypeID"
                            class="modern-checkbox-input"
                            [checked]="selectedPackages.has(package.membershipTypeID)"
                            (change)="onPackageToggle(package.membershipTypeID)"
                          />
                          <label class="modern-checkbox-label" [for]="'package-' + package.membershipTypeID">
                            <span class="checkbox-icon"></span>
                            {{ package.typeName }}
                            <span class="modern-badge modern-badge-success ms-2">{{ package.memberCount }}</span>
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </ng-container>
            </div>
          </div>

          <!-- Paket Filtreleme Ara Butonları -->
          <div class="package-search-actions mt-3" *ngIf="selectedPackages.size > 0">
            <button
              class="modern-btn modern-btn-primary modern-btn-sm search-btn w-100 mb-2"
              (click)="searchBySelectedPackages()"
              [disabled]="isLoading"
            >
              <i class="fas fa-search me-2"></i>
              Ara ({{ selectedPackages.size }} paket)
            </button>
            <button
              class="modern-btn modern-btn-outline-secondary modern-btn-sm w-100"
              (click)="clearPackageSelectionAndSearch()"
              [disabled]="isLoading"
            >
              <i class="fas fa-times me-2"></i>
              Seçimi Temizle
            </button>
          </div>

          <!-- Üye İstatistikleri -->
          <div class="modern-stats-card mt-4">
            <div class="modern-stats-icon bg-primary">
              <i class="fas fa-users"></i>
            </div>
            <div class="modern-stats-info">
              <h2 class="modern-stats-value">{{ totalActiveMembers }}</h2>
              <p class="modern-stats-label">Toplam Aktif Üye</p>
            </div>
          </div>
          
        </div>
      </div>
      
      <!-- Cinsiyet Dağılımı Grafiği -->
      <div class="modern-card gender-chart-card mt-3 slide-in-left">
        <div class="modern-card-header">
          <h5 class="mb-0">
            <i class="fas fa-chart-pie me-2"></i>
            Cinsiyet Dağılımı
          </h5>
        </div>
        <div class="modern-card-body">
          <canvas id="genderChart" width="100%" height="180"></canvas>
        </div>
      </div>
    </div>

    <!-- Üye Listesi -->
    <div class="col-md-9">
      <div class="modern-card member-list-card fade-in" style="position: relative;">
        <!-- Help Button -->
        <app-help-button
          guideId="memberfilter"
          position="top-right"
          size="medium"
          tooltip="Bu panel hakkında yardım al">
        </app-help-button>

        <div class="modern-card-header">
          <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>
            Aktif Üye Listesi
          </h5>
          
          <!-- Arama Kutusu -->
          <div class="search-container">
            <div class="modern-search-input">
              <i class="fas fa-search search-icon"></i>
              <input
                type="text"
                [ngModel]="memberFilterText"
                (ngModelChange)="searchTextChanged($event)"
                placeholder="Ad, Soyad veya Telefon"
              />
            </div>
          </div>
        </div>
        
        <div class="modern-card-body">
          <div class="table-container">
            <table class="modern-table">
              <thead>
                <tr>
                  <th>
                    <i class="fas fa-user me-2"></i>
                    Ad Soyad
                  </th>
                  <th>
                    <i class="fas fa-phone me-2"></i>
                    Telefon
                  </th>
                  <th>
                    <i class="fas fa-dumbbell me-2"></i>
                    Branş
                  </th>
                  <th>
                    <i class="fas fa-calendar-day me-2"></i>
                    Kalan Gün
                  </th>
                  <th>
                    <i class="fas fa-cogs me-2"></i>
                    İşlemler
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let member of members" class="zoom-in">
                  <td>
                    <div class="member-name">
                      <div class="modern-avatar" [ngStyle]="{'background-color': member.gender == 1 ? 'var(--primary)' : '#FF69B4'}">
                        {{ member.name.charAt(0) }}
                      </div>
                      <span>{{ member.name }}</span>
                    </div>
                  </td>
                  <td>{{ member.phoneNumber }}</td>
                  <td>
                    <span class="modern-badge modern-badge-info">{{ member.branch }}</span>
                  </td>
                  <td>
                    <div class="remaining-days">
                      <span [ngClass]="{'text-success': member.remainingDays > 10, 'text-warning': member.remainingDays <= 10 && member.remainingDays > 3, 'text-danger': member.remainingDays <= 3}">
                        {{ member.remainingDays }} gün
                      </span>
                      <span *ngIf="member.isFutureStartDate" class="modern-badge modern-badge-warning ms-2">
                        Başlangıç: {{ member.startDate | date:'dd.MM.yyyy' }}
                      </span>
                    </div>
                  </td>
                  <td>
                    <div class="action-buttons">
                      <button
                        class="modern-btn modern-btn-danger modern-btn-sm"
                        (click)="deleteMember(member)"
                        title="Üyeyi Sil"
                      >
                        <i class="fas fa-trash-alt"></i>
                      </button>
                      <button
                        class="modern-btn modern-btn-info modern-btn-sm ms-2"
                        (click)="openFreezeDialog(member)"
                        [disabled]="member.remainingDays <= 0"
                        title="Üyeliği Dondur"
                      >
                        <i class="fas fa-snowflake"></i>
                      </button>
                    </div>
                  </td>
                </tr>
                
                <!-- Veri yoksa gösterilecek mesaj -->
                <tr *ngIf="members.length === 0">
                  <td colspan="5" class="text-center py-4">
                    <i class="fas fa-search fa-2x mb-2 text-muted"></i>
                    <p class="mb-0">Arama kriterlerine uygun üye bulunamadı.</p>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          
          <!-- Pagination Info and Controls -->
          <div class="pagination-wrapper" *ngIf="totalItems > 0">
            <div class="pagination-info">
              <span class="text-muted">
                Toplam {{ totalItems }} kayıttan
                {{ (currentPage - 1) * pageSize + 1 }}-{{
                  Math.min(currentPage * pageSize, totalItems)
                }} arası gösteriliyor
              </span>
            </div>

            <div class="pagination-controls">
              <!-- Page Size Selector -->
              <div class="page-size-selector">
                <label class="form-label me-2 mb-0">Sayfa başına:</label>
                <select class="form-select form-select-sm"
                        [(ngModel)]="pageSize"
                        (ngModelChange)="changePageSize($event)"
                        style="width: auto; display: inline-block;">
                  <option value="10">10</option>
                  <option value="25">25</option>
                  <option value="50">50</option>
                  <option value="100">100</option>
                </select>
              </div>

              <!-- Sayfalama -->
              <div class="pagination-container" *ngIf="totalPages > 1">
                <ul class="modern-pagination">
                  <li class="modern-page-item" [class.disabled]="currentPage === 1">
                    <a
                      class="modern-page-link"
                      href="javascript:void(0)"
                      (click)="onPageChange(currentPage - 1)"
                    >
                      <i class="fas fa-chevron-left"></i>
                    </a>
                  </li>
                  <li
                    class="modern-page-item"
                    *ngFor="let i of [].constructor(totalPages); let idx = index"
                    [class.active]="currentPage === idx + 1"
                  >
                    <a
                      class="modern-page-link"
                      href="javascript:void(0)"
                      (click)="onPageChange(idx + 1)"
                    >
                      {{ idx + 1 }}
                    </a>
                  </li>
                  <li
                    class="modern-page-item"
                    [class.disabled]="currentPage === totalPages"
                  >
                    <a
                      class="modern-page-link"
                      href="javascript:void(0)"
                      (click)="onPageChange(currentPage + 1)"
                    >
                      <i class="fas fa-chevron-right"></i>
                    </a>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    </div>

  </div>
</div>
