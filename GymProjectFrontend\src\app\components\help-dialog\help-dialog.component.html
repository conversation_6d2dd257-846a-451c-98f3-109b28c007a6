<!-- src/app/components/help-dialog/help-dialog.component.html -->
<div class="modern-dialog zoom-in help-dialog">
  <div class="modern-card-header">
    <div class="header-left">
      <div class="help-icon">
        <i [class]="data.guide.icon"></i>
      </div>
      <div class="header-text">
        <h2>{{ data.guide.title }}</h2>
        <p class="header-subtitle">{{ data.guide.description }}</p>
      </div>
    </div>
    <button class="modern-btn-icon close-btn" (click)="onCloseClick()" *ngIf="data.showCloseButton">
      <i class="fas fa-times"></i>
    </button>
  </div>

  <div class="modern-card-body">
    <div class="help-content">
      <div 
        *ngFor="let content of data.guide.content" 
        class="content-section"
        [ngClass]="getContentTypeClass(content)">
        
        <div class="content-header" *ngIf="content.type !== 'text'">
          <i [class]="getContentTypeIcon(content)"></i>
          <span class="content-type-label">
            {{ content.type === 'steps' ? 'Adımlar' : 
               content.type === 'list' ? 'Özellikler' :
               content.type === 'warning' ? 'Dikkat' :
               content.type === 'tip' ? 'İpucu' : 'Bilgi' }}
          </span>
        </div>

        <!-- Text Content -->
        <div *ngIf="content.type === 'text'" class="text-content">
          <p>{{ content.content }}</p>
        </div>

        <!-- List Content -->
        <ul *ngIf="content.type === 'list' && isArray(content.content)" class="list-content">
          <li *ngFor="let item of content.content">
            <i class="fas fa-check-circle"></i>
            <span>{{ item }}</span>
          </li>
        </ul>

        <!-- Steps Content -->
        <ol *ngIf="content.type === 'steps' && isArray(content.content)" class="steps-content">
          <li *ngFor="let step of content.content">
            <span class="step-text">{{ step }}</span>
          </li>
        </ol>

        <!-- Warning Content -->
        <div *ngIf="content.type === 'warning'" class="warning-content">
          <p>{{ content.content }}</p>
        </div>

        <!-- Tip Content -->
        <div *ngIf="content.type === 'tip'" class="tip-content">
          <p>{{ content.content }}</p>
        </div>
      </div>
    </div>
  </div>

  <div class="modern-card-footer">
    <div class="footer-info">
      <i class="fas fa-info-circle"></i>
      <span>Bu rehber size yardımcı oldu mu? Geri bildirimlerinizi bekliyoruz.</span>
    </div>
    <button 
      type="button" 
      class="modern-btn modern-btn-primary"
      (click)="onCloseClick()">
      <i class="fas fa-check me-2"></i> Anladım
    </button>
  </div>
</div>
